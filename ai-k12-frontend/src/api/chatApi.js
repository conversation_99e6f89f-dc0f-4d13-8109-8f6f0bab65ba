import axios from 'axios'
import { getApiUrl } from '../config/env.js'

const API_BASE_URL = getApiUrl()

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    console.log('发送请求:', config.url, config.data)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => response,
  error => {
    console.error('响应错误:', error)
    return Promise.reject(error)
  }
)

/**
 * K12教育聊天API - 流式对话
 * @param {string} memoryId - 会话ID
 * @param {string} message - 用户消息
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onClose - 关闭回调
 * @returns {EventSource} EventSource实例
 */
export function k12ChatStream(memoryId, message, subject, grade, onMessage, onError, onClose) {
  const url = `${API_BASE_URL}/chat/k12/stream`
  const params = new URLSearchParams({
    memoryId: memoryId || '',
    message: message,
    subject: subject || '',
    grade: grade || ''
  })
  
  const fullUrl = `${url}?${params.toString()}`
  console.log('创建K12聊天流连接:', fullUrl)
  
  const eventSource = new EventSource(fullUrl)
  
  eventSource.onmessage = function(event) {
    try {
      const data = JSON.parse(event.data)
      if (data.type === 'content') {
        onMessage(data.content)
      } else if (data.type === 'end') {
        eventSource.close()
        onClose()
      }
    } catch (e) {
      console.error('解析流数据错误:', e)
      onMessage(event.data)
    }
  }
  
  eventSource.onerror = function(event) {
    console.error('K12聊天流错误:', event)
    onError(new Error('K12教育服务连接失败'))
    eventSource.close()
  }
  
  return eventSource
}

/**
 * 普通聊天API - 兼容原有功能
 * @param {string} memoryId - 会话ID
 * @param {string} message - 用户消息
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onClose - 关闭回调
 * @returns {EventSource} EventSource实例
 */
export function chatWithSSE(memoryId, message, onMessage, onError, onClose) {
  const url = `${API_BASE_URL}/chat/stream`
  const params = new URLSearchParams({
    memoryId: memoryId || '',
    message: message
  })
  
  const fullUrl = `${url}?${params.toString()}`
  console.log('创建普通聊天流连接:', fullUrl)
  
  const eventSource = new EventSource(fullUrl)
  
  eventSource.onmessage = function(event) {
    try {
      const data = JSON.parse(event.data)
      if (data.type === 'content') {
        onMessage(data.content)
      } else if (data.type === 'end') {
        eventSource.close()
        onClose()
      }
    } catch (e) {
      console.error('解析流数据错误:', e)
      onMessage(event.data)
    }
  }
  
  eventSource.onerror = function(event) {
    console.error('普通聊天流错误:', event)
    onError(new Error('聊天服务连接失败'))
    eventSource.close()
  }
  
  return eventSource
}

/**
 * 发送图片消息
 * @param {string} memoryId - 会话ID
 * @param {File} imageFile - 图片文件
 * @param {string} description - 图片描述
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @returns {Promise} 
 */
export async function sendImageMessage(memoryId, imageFile, description, subject, grade) {
  try {
    const formData = new FormData()
    formData.append('image', imageFile)
    formData.append('memoryId', memoryId || '')
    formData.append('description', description || '')
    formData.append('subject', subject || '')
    formData.append('grade', grade || '')
    
    const response = await apiClient.post('/chat/k12/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    return response.data
  } catch (error) {
    console.error('发送图片消息失败:', error)
    throw error
  }
}

/**
 * 获取学习建议
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @param {string} topic - 主题
 * @returns {Promise}
 */
export async function getStudySuggestions(subject, grade, topic) {
  try {
    const response = await apiClient.get('/chat/k12/suggestions', {
      params: { subject, grade, topic }
    })
    return response.data
  } catch (error) {
    console.error('获取学习建议失败:', error)
    throw error
  }
}

/**
 * 获取练习题
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @param {string} concept - 知识点
 * @returns {Promise}
 */
export async function getPracticeQuestions(subject, grade, concept) {
  try {
    const response = await apiClient.get('/chat/k12/practice', {
      params: { subject, grade, concept }
    })
    return response.data
  } catch (error) {
    console.error('获取练习题失败:', error)
    throw error
  }
}

/**
 * 分析学习进度
 * @param {string} memoryId - 会话ID
 * @returns {Promise}
 */
export async function analyzeProgress(memoryId) {
  try {
    const response = await apiClient.get(`/chat/k12/progress/${memoryId}`)
    return response.data
  } catch (error) {
    console.error('分析学习进度失败:', error)
    throw error
  }
}
