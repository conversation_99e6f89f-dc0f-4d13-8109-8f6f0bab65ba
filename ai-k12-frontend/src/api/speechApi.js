import axios from 'axios'
import { getApiUrl } from '../config/env.js'

const API_BASE_URL = getApiUrl()

// 创建axios实例
const speechClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000
})

/**
 * 语音识别API - 将音频转换为文字
 * @param {Blob|File} audioBlob - 音频文件
 * @param {string} language - 语言代码，默认为中文
 * @returns {Promise<{text: string, confidence: number}>}
 */
export async function speechToText(audioBlob, language = 'zh-CN') {
  try {
    const formData = new FormData()
    formData.append('audio', audioBlob, 'recording.wav')
    formData.append('language', language)
    
    const response = await speechClient.post('/speech/stt', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    return response.data
  } catch (error) {
    console.error('语音识别失败:', error)
    throw new Error('语音识别服务暂时不可用')
  }
}

/**
 * 语音合成API - 将文字转换为语音
 * @param {string} text - 要合成的文字
 * @param {Object} options - 合成选项
 * @param {string} options.voice - 声音类型
 * @param {number} options.speed - 语速 (0.5-2.0)
 * @param {number} options.pitch - 音调 (0.5-2.0)
 * @param {string} options.format - 音频格式 (mp3, wav)
 * @returns {Promise<Blob>} 音频文件Blob
 */
export async function textToSpeech(text, options = {}) {
  try {
    const {
      voice = 'zh-CN-XiaoxiaoNeural',
      speed = 1.0,
      pitch = 1.0,
      format = 'mp3'
    } = options
    
    const response = await speechClient.post('/speech/tts', {
      text,
      voice,
      speed,
      pitch,
      format
    }, {
      responseType: 'blob'
    })
    
    return response.data
  } catch (error) {
    console.error('语音合成失败:', error)
    throw new Error('语音合成服务暂时不可用')
  }
}

/**
 * 获取可用的语音列表
 * @param {string} language - 语言代码
 * @returns {Promise<Array>} 语音列表
 */
export async function getAvailableVoices(language = 'zh-CN') {
  try {
    const response = await speechClient.get('/speech/voices', {
      params: { language }
    })
    
    return response.data
  } catch (error) {
    console.error('获取语音列表失败:', error)
    return []
  }
}

/**
 * 检查语音服务状态
 * @returns {Promise<boolean>} 服务是否可用
 */
export async function checkSpeechServiceStatus() {
  try {
    const response = await speechClient.get('/speech/status')
    return response.data.available || false
  } catch (error) {
    console.error('检查语音服务状态失败:', error)
    return false
  }
}

/**
 * 语音评测API - 用于口语练习评分
 * @param {Blob|File} audioBlob - 音频文件
 * @param {string} referenceText - 参考文本
 * @param {string} language - 语言代码
 * @returns {Promise<Object>} 评测结果
 */
export async function speechEvaluation(audioBlob, referenceText, language = 'zh-CN') {
  try {
    const formData = new FormData()
    formData.append('audio', audioBlob, 'pronunciation.wav')
    formData.append('reference', referenceText)
    formData.append('language', language)
    
    const response = await speechClient.post('/speech/evaluation', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    return response.data
  } catch (error) {
    console.error('语音评测失败:', error)
    throw new Error('语音评测服务暂时不可用')
  }
}

// Web Speech API 相关工具函数

/**
 * 检查浏览器是否支持Web Speech API
 * @returns {Object} 支持情况
 */
export function checkWebSpeechSupport() {
  return {
    speechRecognition: 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window,
    speechSynthesis: 'speechSynthesis' in window
  }
}

/**
 * 创建浏览器内置语音识别实例
 * @param {Object} options - 配置选项
 * @returns {SpeechRecognition|null}
 */
export function createSpeechRecognition(options = {}) {
  const support = checkWebSpeechSupport()
  if (!support.speechRecognition) {
    console.warn('浏览器不支持语音识别')
    return null
  }
  
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  const recognition = new SpeechRecognition()
  
  // 默认配置
  recognition.continuous = options.continuous || false
  recognition.interimResults = options.interimResults || true
  recognition.lang = options.language || 'zh-CN'
  recognition.maxAlternatives = options.maxAlternatives || 1
  
  return recognition
}

/**
 * 使用浏览器内置TTS播放文本
 * @param {string} text - 要播放的文本
 * @param {Object} options - 播放选项
 * @returns {Promise<void>}
 */
export function speakText(text, options = {}) {
  return new Promise((resolve, reject) => {
    const support = checkWebSpeechSupport()
    if (!support.speechSynthesis) {
      reject(new Error('浏览器不支持语音合成'))
      return
    }
    
    // 停止当前播放
    speechSynthesis.cancel()
    
    const utterance = new SpeechSynthesisUtterance(text)
    
    // 配置选项
    utterance.lang = options.language || 'zh-CN'
    utterance.rate = options.rate || 1.0
    utterance.pitch = options.pitch || 1.0
    utterance.volume = options.volume || 1.0
    
    // 如果指定了声音，尝试使用
    if (options.voice) {
      const voices = speechSynthesis.getVoices()
      const selectedVoice = voices.find(voice => 
        voice.name === options.voice || voice.voiceURI === options.voice
      )
      if (selectedVoice) {
        utterance.voice = selectedVoice
      }
    }
    
    utterance.onend = () => resolve()
    utterance.onerror = (event) => reject(new Error(`语音播放失败: ${event.error}`))
    
    speechSynthesis.speak(utterance)
  })
}

/**
 * 获取浏览器可用的TTS声音列表
 * @returns {Promise<Array>} 声音列表
 */
export function getBrowserVoices() {
  return new Promise((resolve) => {
    const support = checkWebSpeechSupport()
    if (!support.speechSynthesis) {
      resolve([])
      return
    }
    
    let voices = speechSynthesis.getVoices()
    
    if (voices.length === 0) {
      // 某些浏览器需要等待voices加载
      speechSynthesis.onvoiceschanged = () => {
        voices = speechSynthesis.getVoices()
        resolve(voices)
      }
    } else {
      resolve(voices)
    }
  })
}

/**
 * 停止当前TTS播放
 */
export function stopSpeaking() {
  const support = checkWebSpeechSupport()
  if (support.speechSynthesis) {
    speechSynthesis.cancel()
  }
}
