import axios from 'axios'
import { getApiUrl } from '../config/env.js'

const API_BASE_URL = getApiUrl()

// 创建axios实例
const ragClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json'
  }
})

/**
 * K12 RAG教育聊天流式API - 调用后端 /ai/rag/chat-stream 接口
 * @param {string} memoryId - 会话ID
 * @param {string} message - 用户消息
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onClose - 关闭回调
 * @returns {EventSource} EventSource实例
 */
export function k12RagChatStream(memoryId, message, subject, grade, onMessage, onError, onClose) {
  // 构建完整的消息，包含年级和科目信息
  const enhancedMessage = subject && grade
    ? `[${grade} ${subject}] ${message}`
    : message

  // 确保memoryId是有效的数字字符串
  const validMemoryId = memoryId || Date.now().toString()

  const params = new URLSearchParams({
    memoryId: validMemoryId,
    message: enhancedMessage
  })

  const fullUrl = `${API_BASE_URL}/ai/rag/chat-stream?${params.toString()}`
  console.log('创建K12 RAG流连接:', fullUrl)

  const eventSource = new EventSource(fullUrl)

  // 设置超时处理（60秒无响应则认为连接异常）
  let timeoutId = null
  let hasReceivedData = false

  const resetTimeout = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => {
      if (!hasReceivedData) {
        console.error('RAG流式响应超时，未收到任何数据')
        eventSource.close()
        onError && onError(new Error('响应超时，请检查网络连接或稍后重试'))
      }
    }, 60000) // 60秒超时
  }

  resetTimeout()
  
  eventSource.onmessage = function(event) {
    try {
      hasReceivedData = true
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }

      const data = event.data

      // 检查是否是流结束标识
      if (data === '[DONE]' || event.type === 'end') {
        console.log('RAG流式响应正常结束')
        eventSource.close()
        onClose && onClose()
        return
      }

      // 检查是否是错误消息
      if (data && data.startsWith('{"error"')) {
        const errorData = JSON.parse(data)
        console.error('RAG服务端错误:', errorData.error)
        onError && onError(new Error(errorData.error))
        eventSource.close()
        return
      }

      // 正常的流式内容
      if (data && data.trim() !== '') {
        onMessage && onMessage(data)
      }
    } catch (error) {
      console.error('解析RAG消息失败:', error)
      onError && onError(error)
    }
  }
  
  eventSource.onerror = function(error) {
    console.log('RAG SSE 连接状态:', eventSource.readyState)

    // 清理超时定时器
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }

    // 只有在连接未正常关闭时才报告错误
    if (eventSource.readyState === EventSource.CONNECTING) {
      console.error('RAG SSE 连接失败:', error)
      onError && onError(new Error('无法连接到K12教育知识库服务'))
    } else if (eventSource.readyState === EventSource.OPEN) {
      console.error('RAG SSE 连接中断:', error)
      onError && onError(new Error('K12教育知识库服务连接中断'))
    } else {
      console.log('RAG SSE 连接正常关闭')
    }

    // 确保连接被关闭
    if (eventSource.readyState !== EventSource.CLOSED) {
      eventSource.close()
    }
  }

  // 处理连接关闭
  eventSource.addEventListener('close', function() {
    console.log('RAG SSE 连接已关闭')
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    onClose && onClose()
  })

  // 返回EventSource实例，同时提供清理方法
  const originalClose = eventSource.close.bind(eventSource)
  eventSource.close = function() {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    originalClose()
  }

  return eventSource
}

/**
 * 兼容原有RAG聊天API
 * @param {string} memoryId - 会话ID
 * @param {string} message - 用户消息
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onClose - 关闭回调
 * @returns {EventSource} EventSource实例
 */
export function ragChatStream(memoryId, message, onMessage, onError, onClose) {
  return k12RagChatStream(memoryId, message, '', '', onMessage, onError, onClose)
}

/**
 * 非流式RAG聊天（用于特殊场景）
 * @param {string} memoryId - 会话ID
 * @param {string} message - 用户消息
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @returns {Promise}
 */
export async function k12RagChat(memoryId, message, subject, grade) {
  try {
    const response = await ragClient.post('/rag/k12/chat', {
      memoryId: memoryId || '',
      message: message,
      subject: subject || '',
      grade: grade || ''
    })
    
    return response.data
  } catch (error) {
    console.error('K12 RAG聊天失败:', error)
    throw error
  }
}

/**
 * 兼容原有非流式RAG聊天
 * @param {string} memoryId - 会话ID
 * @param {string} message - 用户消息
 * @returns {Promise}
 */
export async function ragChat(memoryId, message) {
  return k12RagChat(memoryId, message, '', '')
}

/**
 * 搜索K12教育知识库
 * @param {string} query - 搜索查询
 * @param {string} subject - 科目筛选
 * @param {string} grade - 年级筛选
 * @param {number} limit - 结果数量限制
 * @returns {Promise}
 */
export async function searchK12Knowledge(query, subject = '', grade = '', limit = 10) {
  try {
    const response = await ragClient.get('/rag/k12/search', {
      params: { 
        query, 
        subject, 
        grade, 
        limit 
      }
    })
    
    return response.data
  } catch (error) {
    console.error('搜索K12知识库失败:', error)
    throw error
  }
}

/**
 * 获取相关知识点
 * @param {string} concept - 概念或知识点
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @returns {Promise}
 */
export async function getRelatedConcepts(concept, subject, grade) {
  try {
    const response = await ragClient.get('/rag/k12/concepts', {
      params: { concept, subject, grade }
    })
    
    return response.data
  } catch (error) {
    console.error('获取相关知识点失败:', error)
    throw error
  }
}

/**
 * 获取学习路径建议
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @param {string} currentTopic - 当前学习主题
 * @returns {Promise}
 */
export async function getLearningPath(subject, grade, currentTopic) {
  try {
    const response = await ragClient.get('/rag/k12/learning-path', {
      params: { subject, grade, currentTopic }
    })
    
    return response.data
  } catch (error) {
    console.error('获取学习路径失败:', error)
    throw error
  }
}

/**
 * 分析答题情况并给出反馈
 * @param {string} question - 题目
 * @param {string} userAnswer - 用户答案
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @returns {Promise}
 */
export async function analyzeAnswer(question, userAnswer, subject, grade) {
  try {
    const response = await ragClient.post('/rag/k12/analyze-answer', {
      question,
      userAnswer,
      subject,
      grade
    })
    
    return response.data
  } catch (error) {
    console.error('分析答题情况失败:', error)
    throw error
  }
}

/**
 * 获取错题本
 * @param {string} memoryId - 会话ID
 * @param {string} subject - 科目筛选
 * @returns {Promise}
 */
export async function getWrongQuestions(memoryId, subject = '') {
  try {
    const response = await ragClient.get('/rag/k12/wrong-questions', {
      params: { memoryId, subject }
    })
    
    return response.data
  } catch (error) {
    console.error('获取错题本失败:', error)
    throw error
  }
}

