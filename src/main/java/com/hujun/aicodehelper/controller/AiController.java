package com.hujun.aicodehelper.controller;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hujun.aicodehelper.ai.AiCodeHelperService;
import com.hujun.aicodehelper.ai.RagService;
import com.hujun.aicodehelper.services.NetworkTestService;
import com.hujun.aicodehelper.services.PdfGenerationService;
import com.hujun.aicodehelper.services.SpeechSynthesisService;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
@RestController
@RequestMapping("/ai")
public class AiController {

    @Resource
    private AiCodeHelperService aiCodeHelperService;

    @Resource
    private RagService ragService;

    @Resource
    private SpeechSynthesisService speechSynthesisService;

    @Resource
    private NetworkTestService networkTestService;

    @org.springframework.beans.factory.annotation.Value("${speech.provider:azure}")
    private String speechProvider;

    @org.springframework.beans.factory.annotation.Value("${speech.tts.ollama.base-url:http://localhost:11434}")
    private String ollamaBaseUrl;

    @Resource
    private PdfGenerationService pdfGenerationService;

    @GetMapping("/chat")
    public Flux<ServerSentEvent<String>> chat(int memoryId, String message) {
        return aiCodeHelperService.chatStream(memoryId, message)
                .map(chunk -> ServerSentEvent.<String>builder()
                        .data(chunk)
                        .build());
    }

    /**
     * RAG增强问答接口 - 同步版本
     * 基于知识库进行问答，返回答案和相关文档来源
     */
    @PostMapping("/rag/chat")
    public Map<String, Object> ragChat(@RequestBody RagChatRequest request) {
        RagService.RagChatResult result = ragService.chatWithRag(request.getMessage());

        Map<String, Object> response = new HashMap<>();
        response.put("answer", result.getAnswer());
        response.put("sources", result.getSources());
        response.put("success", true);

        return response;
    }

    /**
     * RAG增强问答接口 - 流式版本
     * 基于知识库进行问答，实时返回流式响应
     */
    @GetMapping("/rag/chat-stream")
    public Flux<ServerSentEvent<String>> ragChatStream(@RequestParam String memoryId, @RequestParam String message) {
        log.info("🚀 收到RAG流式聊天请求 [会话:{}]: {}", memoryId, message.substring(0, Math.min(50, message.length())));

        try {
            // 验证输入参数
            if (message == null || message.trim().isEmpty()) {
                log.warn("⚠️ RAG流式聊天消息为空");
                return Flux.just(ServerSentEvent.<String>builder()
                        .data("{\"error\": \"消息内容不能为空\"}")
                        .build());
            }

            // 将字符串memoryId转换为int，如果转换失败则使用默认值
            int tempMemoryId;
            try {
                tempMemoryId = Integer.parseInt(memoryId);
            } catch (NumberFormatException e) {
                // 如果无法解析为int，使用hashCode作为替代
                tempMemoryId = Math.abs(memoryId.hashCode());
                log.warn("无法解析memoryId为int: {}, 使用hashCode: {}", memoryId, tempMemoryId);
            }
            final int memoryIdInt = tempMemoryId;

            log.info("📝 开始处理RAG流式聊天 [会话:{}]", memoryIdInt);

            // 注意：这里我们使用普通的流式聊天，因为LangChain4j的RAG流式返回比较复杂
            // 实际应用中，RAG增强的内容会在后台自动注入到对话上下文中
            return aiCodeHelperService.chatStream(memoryIdInt, message.trim())
                    .filter(chunk -> chunk != null && !chunk.trim().isEmpty()) // 过滤空内容
                    .map(chunk -> ServerSentEvent.<String>builder()
                            .data(chunk)
                            .build())
                    .concatWith(Flux.just(ServerSentEvent.<String>builder()
                            .event("end")
                            .data("[DONE]")
                            .build())) // 添加流结束标识
                    .doOnComplete(() -> {
                        log.debug("RAG流式聊天完成，会话: {}", memoryIdInt);
                    })
                    .doOnError(error -> {
                        log.error("RAG流式聊天出错，会话: {}, 错误: {}", memoryIdInt, error.getMessage());
                    })
                    .onErrorResume(throwable -> {
                        log.error("RAG流式聊天发生错误: {}", throwable.getMessage());
                        return Flux.just(ServerSentEvent.<String>builder()
                                .data("{\"error\": \"处理请求时发生错误，请稍后重试\"}")
                                .build());
                    });
        } catch (Exception e) {
            log.error("RAG流式聊天参数处理错误: {}", e.getMessage());
            return Flux.just(ServerSentEvent.<String>builder()
                    .data("{\"error\": \"参数处理错误\"}")
                    .build());
        }
    }

    /**
     * 知识库搜索接口
     * 直接搜索知识库中的相关内容，不进行对话生成
     */
    @PostMapping("/rag/search")
    public Map<String, Object> searchKnowledge(@RequestBody KnowledgeSearchRequest request) {
        List<RagService.RagSearchResult> results = ragService.searchKnowledge(
                request.getQuery(),
                request.getMaxResults(),
                request.getMinScore());

        Map<String, Object> response = new HashMap<>();
        response.put("results", results);
        response.put("success", true);
        response.put("query", request.getQuery());
        response.put("total", results.size());

        return response;
    }

    /**
     * 获取知识库统计信息
     */
    @GetMapping("/rag/stats")
    public RagService.KnowledgeStats getKnowledgeStats() {
        return ragService.getKnowledgeStats();
    }

    /**
     * 将AI生成的内容转换为语音
     * 适用于AI通过RAG生成的文章内容
     */
    @PostMapping("/speech/synthesize")
    public Map<String, Object> synthesizeSpeech(@RequestBody SpeechSynthesisRequest request) {
        try {
            log.info("收到语音合成请求，文本长度: {}, 标题: {}",
                    request.getText() != null ? request.getText().length() : 0,
                    request.getTitle());

            SpeechSynthesisService.SpeechSynthesisResult result = speechSynthesisService
                    .synthesizeArticle(request.getText(), request.getTitle());

            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());

            if (result.isSuccess()) {
                response.put("filePath", result.getFilePath());
                response.put("fileName", result.getFileName());
                response.put("fileSize", result.getFileSize());
            }

            return response;

        } catch (Exception e) {
            log.error("语音合成异常: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "语音合成异常: " + e.getMessage());
            return response;
        }
    }

    /**
     * 支持自定义语音的语音合成接口
     */
    @PostMapping("/speech/synthesize-with-voice")
    public Map<String, Object> synthesizeSpeechWithVoice(@RequestBody SpeechSynthesisWithVoiceRequest request) {
        try {
            log.info("收到自定义语音合成请求，文本长度: {}, 语音: {}",
                    request.getText() != null ? request.getText().length() : 0,
                    request.getVoiceName());

            // 使用自定义语音的语音合成方法
            SpeechSynthesisService.SpeechSynthesisResult result = speechSynthesisService.synthesizeArticleWithVoice(
                    request.getText(),
                    request.getTitle(),
                    request.getVoiceName());

            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());

            if (result.isSuccess()) {
                response.put("filePath", result.getFilePath());
                response.put("fileName", result.getFileName());
                response.put("fileSize", result.getFileSize());
            }

            return response;

        } catch (Exception e) {
            log.error("自定义语音合成异常: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "语音合成异常: " + e.getMessage());
            return response;
        }
    }

    /**
     * 获取支持的语音列表
     */
    @GetMapping("/speech/voices")
    public Map<String, Object> getSupportedVoices() {
        Map<String, Object> response = new HashMap<>();
        Map<String, String> voices = new HashMap<>();

        voices.put("zh-CN-XiaoxiaoNeural", "中文女声（晓晓）");
        voices.put("zh-CN-YunxiNeural", "中文男声（云希）");
        voices.put("zh-CN-YunyangNeural", "中文男声（云扬）");
        voices.put("en-US-AriaNeural", "英文女声（Aria）");
        voices.put("en-US-JennyNeural", "英文女声（Jenny）");
        voices.put("en-US-GuyNeural", "英文男声（Guy）");

        response.put("success", true);
        response.put("voices", voices);
        response.put("defaultVoice", "zh-CN-XiaoxiaoNeural");

        return response;
    }

    /**
     * 测试Azure语音服务网络连接
     */
    @GetMapping("/speech/test-network")
    public Map<String, Object> testNetworkConnectivity() {
        try {
            networkTestService.checkNetworkConfig();

            boolean connectivityOk;
            if ("ollama".equalsIgnoreCase(speechProvider)) {
                connectivityOk = networkTestService.testOllamaConnectivity(ollamaBaseUrl);
            } else {
                connectivityOk = networkTestService.testAzureSpeechConnectivity("eastus");
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", connectivityOk);
            response.put("message", connectivityOk ? "网络连接正常" : "网络连接异常，请检查防火墙和代理设置");
            response.put("timestamp", System.currentTimeMillis());

            return response;

        } catch (Exception e) {
            log.error("网络连接测试异常: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "网络测试异常: " + e.getMessage());
            return response;
        }
    }

    /**
     * 下载语音文件
     */
    @GetMapping("/speech/download")
    public ResponseEntity<FileSystemResource> downloadSpeechFile(@RequestParam String fileName) {
        try {
            // 验证文件名安全性
            if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                return ResponseEntity.badRequest().build();
            }

            // 构建文件路径
            Path filePath = Paths.get("./data/speech-output", fileName);
            File file = filePath.toFile();

            if (!file.exists() || !file.isFile()) {
                return ResponseEntity.notFound().build();
            }

            FileSystemResource resource = new FileSystemResource(file);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, "audio/wav")
                    .contentLength(file.length())
                    .body(resource);

        } catch (Exception e) {
            log.error("下载语音文件异常: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取语音文件列表
     */
    @GetMapping("/speech/files")
    public Map<String, Object> getSpeechFiles() {
        try {
            Path speechDir = Paths.get("./data/speech-output");
            List<Map<String, Object>> files = new ArrayList<>();

            if (Files.exists(speechDir)) {
                Files.list(speechDir)
                        .filter(path -> path.toString().endsWith(".wav"))
                        .forEach(path -> {
                            File file = path.toFile();
                            Map<String, Object> fileInfo = new HashMap<>();
                            fileInfo.put("name", file.getName());
                            fileInfo.put("size", file.length());
                            fileInfo.put("lastModified", file.lastModified());
                            files.add(fileInfo);
                        });
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("files", files);
            response.put("total", files.size());

            return response;

        } catch (IOException e) {
            log.error("获取语音文件列表异常: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取文件列表失败");
            return response;
        }
    }

    /**
     * RAG聊天请求体
     */
    public static class RagChatRequest {
        private String message;
        private String context; // 可选的上下文信息

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getContext() {
            return context;
        }

        public void setContext(String context) {
            this.context = context;
        }
    }

    /**
     * 知识库搜索请求体
     */
    public static class KnowledgeSearchRequest {
        private String query;
        private int maxResults = 5; // 最大返回结果数
        private double minScore = 0.7; // 最小相似度分数

        public String getQuery() {
            return query;
        }

        public void setQuery(String query) {
            this.query = query;
        }

        public int getMaxResults() {
            return maxResults;
        }

        public void setMaxResults(int maxResults) {
            this.maxResults = maxResults;
        }

        public double getMinScore() {
            return minScore;
        }

        public void setMinScore(double minScore) {
            this.minScore = minScore;
        }
    }

    /**
     * 语音合成请求体
     */
    public static class SpeechSynthesisRequest {
        private String text;
        private String title;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }
    }

    /**
     * PDF导出接口
     * 将AI回复内容生成为PDF文档供下载
     */
    @PostMapping("/export/pdf")
    public ResponseEntity<byte[]> exportToPdf(@RequestBody PdfExportRequest request) {
        try {
            log.info("收到PDF导出请求，标题: {}, 内容长度: {}",
                    request.getTitle(),
                    request.getContent() != null ? request.getContent().length() : 0);

            // 验证输入参数
            if (request.getContent() == null || request.getContent().trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            // 生成PDF
            String title = request.getTitle() != null && !request.getTitle().trim().isEmpty()
                    ? request.getTitle()
                    : "AI回复内容";

            byte[] pdfBytes = pdfGenerationService.generatePdf(request.getContent(), title);

            // 构建文件名
            String fileName = title.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_") + ".pdf";

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentType(org.springframework.http.MediaType.APPLICATION_PDF);
            headers.setContentLength(pdfBytes.length);

            log.info("PDF生成成功，文件大小: {} bytes", pdfBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("PDF生成失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * PDF导出请求体
     */
    public static class PdfExportRequest {
        private String content;
        private String title;

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }
    }

    /**
     * 自定义语音合成请求体
     */
    public static class SpeechSynthesisWithVoiceRequest {
        private String text;
        private String title;
        private String voiceName;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getVoiceName() {
            return voiceName;
        }

        public void setVoiceName(String voiceName) {
            this.voiceName = voiceName;
        }
    }
}
