package com.hujun.aicodehelper.ai;

import com.hujun.aicodehelper.ai.guardrail.SafeInputGuardrail;
import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy;
import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType;
import dev.langchain4j.service.*;
import dev.langchain4j.service.guardrail.InputGuardrails;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import reactor.util.retry.Retry;

/**
 * 智能AI代码助手服务
 * 实现动态模型切换，根据请求自动选择最合适的模型
 */
@Slf4j
@Service("aiCodeHelperService") // 明确指定Bean名称
@InputGuardrails({SafeInputGuardrail.class})
public class SmartAiCodeHelperService implements AiCodeHelperService {

    @Resource
    private DynamicAiServiceManager dynamicAiServiceManager;

    @Resource
    private ModelSelectionStrategy modelSelectionStrategy;

    // 会话记忆缓存，用于跟踪对话历史
    private final Map<Integer, List<String>> conversationHistory = new ConcurrentHashMap<>();

    // 工具调用监听器，用于检测工具使用情况
    private final Map<Integer, String> lastToolUsed = new ConcurrentHashMap<>();

    @Override
    public String chat(String userMessage) {
        log.info("收到聊天请求: {}", userMessage.substring(0, Math.min(50, userMessage.length())));
        
        // 获取合适的AI服务
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiService(userMessage, null);
        
        // 执行聊天
        return aiService.chat(userMessage);
    }

    @Override
    public Report chatForReport(String userMessage) {
        log.info("收到报告生成请求: {}", userMessage.substring(0, Math.min(50, userMessage.length())));
        
        // 报告生成通常需要更强的模型
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiServiceByModelType(ModelType.PRO);
        
        return aiService.chatForReport(userMessage);
    }

    @Override
    public Result<String> chatWithRag(String userMessage) {
        log.info("收到RAG聊天请求: {}", userMessage.substring(0, Math.min(50, userMessage.length())));
        
        // RAG查询使用专门的RAG模型
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiServiceByModelType(ModelType.RAG);
        
        return aiService.chatWithRag(userMessage);
    }

    @Override
    public Flux<String> chatStream(int memoryId, String userMessage) {
        log.info("收到流式聊天请求 [会话:{}]: {}", memoryId, userMessage.substring(0, Math.min(50, userMessage.length())));
        
        // 获取该会话的历史记录
        List<String> history = getConversationHistory(memoryId);
        
        // 智能选择模型类型（考虑消息内容、历史记录和工具使用）
        ModelType selectedModelType = optimizeModelSelection(userMessage, history, memoryId);
        
        // 获取合适的AI服务
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiServiceByModelType(selectedModelType);
        
        // 更新会话历史
        updateConversationHistory(memoryId, userMessage);
        
        log.info("为会话 {} 选择模型: {}", memoryId, selectedModelType.getDescription());
        
        return aiService.chatStream(memoryId, userMessage)
                .doOnNext(chunk -> {
                    // 监听响应中的工具调用
                    detectToolUsageInResponse(memoryId, chunk);
                })
                .doOnComplete(() -> {
                    log.debug("流式响应完成，会话: {}", memoryId);
                })
                .doOnError(error -> {
                    log.error("流式响应出错，会话: {}, 错误: {}", memoryId, error.getMessage());
                });
    }

    /**
     * 获取可用的模型信息
     */
    public Map<String, Object> getModelInfo() {
        Map<String, Object> info = new ConcurrentHashMap<>();
        
        // 模型可用性
        Map<ModelType, Boolean> availability = dynamicAiServiceManager.getModelAvailability();
        info.put("modelAvailability", availability);
        
        // 可用模型类型
        List<ModelType> availableTypes = dynamicAiServiceManager.getAvailableModelTypes();
        info.put("availableModels", availableTypes);
        
        // 当前活跃的会话数
        info.put("activeSessions", conversationHistory.size());
        
        return info;
    }

    /**
     * 强制切换指定会话的模型
     */
    public void forceModelSwitch(int memoryId, ModelType modelType) {
        log.info("强制切换会话 {} 的模型为: {}", memoryId, modelType.getDescription());
        lastToolUsed.put(memoryId, "FORCE_SWITCH_" + modelType.name());
    }

    /**
     * 清理会话历史
     */
    public void clearSessionHistory(int memoryId) {
        conversationHistory.remove(memoryId);
        lastToolUsed.remove(memoryId);
        log.info("清理会话历史: {}", memoryId);
    }

    /**
     * 预热所有模型
     */
    public void warmUpAllModels() {
        log.info("开始预热所有AI模型...");
        dynamicAiServiceManager.warmUpServices();
    }

    /**
     * 获取会话的对话历史
     */
    private List<String> getConversationHistory(int memoryId) {
        return conversationHistory.computeIfAbsent(memoryId, k -> new ArrayList<>());
    }

    /**
     * 更新会话的对话历史
     */
    private void updateConversationHistory(int memoryId, String userMessage) {
        List<String> history = getConversationHistory(memoryId);
        history.add(userMessage);
        
        // 限制历史记录长度，避免内存溢出
        if (history.size() > 20) {
            history.remove(0);
        }
    }

    /**
     * 检测消息应该使用的模型类型
     */
    private ModelType detectModelTypeForMessage(String userMessage, List<String> history) {
        // 使用模型选择策略进行智能检测
        return modelSelectionStrategy.selectModel(userMessage, history);
    }

    /**
     * 检测响应中的工具调用
     */
    private void detectToolUsageInResponse(int memoryId, String responseChunk) {
        // 检测响应中是否包含工具调用的标识
        if (responseChunk.contains("dictionarySearch") || 
            responseChunk.contains("Cambridge Dictionary") ||
            responseChunk.contains("词典查询")) {
            lastToolUsed.put(memoryId, "dictionarySearch");
        }
    }

    /**
     * 检测工具使用并更新模型选择
     */
    private void handleToolUsage(int memoryId, String toolName) {
        lastToolUsed.put(memoryId, toolName);
        
        // 基于工具使用情况，可以为下次对话预选模型
        if ("dictionarySearch".equals(toolName)) {
            log.debug("会话 {} 使用了词典工具，建议后续使用词典模型", memoryId);
        }
    }

    /**
     * 根据会话历史和工具使用情况优化模型选择
     */
    private ModelType optimizeModelSelection(String userMessage, List<String> history, int memoryId) {
        // 首先使用基础策略
        ModelType baseSelection = modelSelectionStrategy.selectModel(userMessage, history);
        
        // 考虑最近的工具使用情况
        String lastTool = lastToolUsed.get(memoryId);
        if (lastTool != null) {
            ModelType toolBasedSelection = modelSelectionStrategy.selectModelForTool(lastTool);
            
            // 如果基础选择是LITE，但最近使用了特殊工具，可能需要调整
            if (baseSelection == ModelType.LITE && toolBasedSelection != ModelType.LITE) {
                log.debug("基于工具使用历史，将模型从 {} 调整为 {}", baseSelection, toolBasedSelection);
                return toolBasedSelection;
            }
        }
        
        return baseSelection;
    }
}