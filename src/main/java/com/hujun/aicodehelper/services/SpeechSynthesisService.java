package com.hujun.aicodehelper.services;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.concurrent.ExecutionException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.microsoft.cognitiveservices.speech.CancellationReason;
import com.microsoft.cognitiveservices.speech.ResultReason;
import com.microsoft.cognitiveservices.speech.SpeechConfig;
import com.microsoft.cognitiveservices.speech.SpeechSynthesisCancellationDetails;
import com.microsoft.cognitiveservices.speech.SpeechSynthesizer;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SpeechSynthesisService {

    @Value("${azure.speech.key:7A8TxwhGk3Ri67yTEz9Z1Ak8kG1mK4fWNWI9xM0CBNisUvVY7RpFJQQJ99BEACYeBjFXJ3w3AAAYACOGNLMK}")
    private String speechKey;

    @Value("${azure.speech.region:eastus}")
    private String speechRegion;

    @Value("${azure.speech.voice:zh-CN-XiaoxiaoNeural}")
    private String defaultVoice;

    // 当前语音提供者: azure | ollama
    @Value("${speech.provider:azure}")
    private String speechProvider;

    // Ollama TTS 配置
    @Value("${speech.tts.ollama.base-url:http://localhost:11434}")
    private String ollamaBaseUrl;

    @Value("${speech.tts.ollama.model-name:gemma3n:e4b}")
    private String ollamaModelName;

    @Value("${speech.tts.ollama.format:wav}")
    private String ollamaFormat;

    @Value("${speech.tts.ollama.sample-rate:24000}")
    private int ollamaSampleRate;

    @Value("${speech.tts.ollama.output-encoding:pcm16}")
    private String ollamaOutputEncoding;

    @Value("${speech.output.directory:./data/speech-output}")
    private String outputDirectory;

    /**
     * 语音合成结果
     */
    public static class SpeechSynthesisResult {
        private boolean success;
        private String message;
        private String filePath;
        private String fileName;
        private long fileSize;

        public SpeechSynthesisResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public SpeechSynthesisResult(boolean success, String message, String filePath, String fileName, long fileSize) {
            this.success = success;
            this.message = message;
            this.filePath = filePath;
            this.fileName = fileName;
            this.fileSize = fileSize;
        }

        // Getters and setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public long getFileSize() {
            return fileSize;
        }

        public void setFileSize(long fileSize) {
            this.fileSize = fileSize;
        }
    }

    /**
     * 初始化输出目录
     */
    private void initializeOutputDirectory() throws IOException {
        Path path = Paths.get(outputDirectory);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
            log.info("创建语音输出目录: {}", outputDirectory);
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String prefix) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return String.format("%s_%s.wav", prefix, timestamp);
    }

    /**
     * 为AI生成的文章创建语音文件
     * 
     * @param articleText  AI生成的文章内容
     * @param articleTitle 文章标题（可选，用于文件命名）
     * @return 语音合成结果
     */
    public SpeechSynthesisResult synthesizeArticle(String articleText, String articleTitle) {
        if (articleText == null || articleText.trim().isEmpty()) {
            return new SpeechSynthesisResult(false, "文章内容不能为空");
        }

        try {
            initializeOutputDirectory();

            // 生成文件名
            String prefix = (articleTitle != null && !articleTitle.trim().isEmpty())
                    ? articleTitle.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_")
                    : "article";
            String fileName = generateFileName(prefix);
            String filePath = Paths.get(outputDirectory, fileName).toString();

            return synthesizeSpeech(articleText, filePath, defaultVoice);

        } catch (IOException e) {
            log.error("初始化输出目录失败: {}", e.getMessage());
            return new SpeechSynthesisResult(false, "初始化输出目录失败: " + e.getMessage());
        }
    }

    /**
     * 语音合成核心方法
     * 
     * @param text           要合成的文本
     * @param outputFilePath 输出文件路径
     * @param voiceName      语音名称
     * @return 语音合成结果
     */
    public SpeechSynthesisResult synthesizeSpeech(String text, String outputFilePath, String voiceName) {
        // 如果配置使用本地 Ollama，则走 Ollama 路径
        if ("ollama".equalsIgnoreCase(speechProvider)) {
            return synthesizeWithOllama(text, outputFilePath);
        }

        int maxRetries = 3;
        int retryDelay = 2000; // 2秒

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("开始语音合成 (尝试 {}/{}), 文本长度: {}, 输出文件: {}",
                        attempt, maxRetries, text.length(), outputFilePath);

                // 配置Azure语音服务
                SpeechConfig speechConfig = SpeechConfig.fromSubscription(speechKey, speechRegion);
                speechConfig.setSpeechSynthesisVoiceName(voiceName != null ? voiceName : defaultVoice);

                // 添加连接超时设置
                speechConfig.setProperty("Speech_LogFilename", "speech.log");
                speechConfig.setProperty("SPEECH_SYNTHESIS_CONNECTION_TIMEOUT_MS", "10000");

                // 配置音频输出
                AudioConfig audioConfig = AudioConfig.fromWavFileOutput(outputFilePath);

                // 执行语音合成
                com.microsoft.cognitiveservices.speech.SpeechSynthesisResult result;
                try (SpeechSynthesizer speechSynthesizer = new SpeechSynthesizer(speechConfig, audioConfig)) {
                    result = speechSynthesizer.SpeakTextAsync(text).get(30, java.util.concurrent.TimeUnit.SECONDS);
                }

                if (result.getReason() == ResultReason.SynthesizingAudioCompleted) {
                    File outputFile = new File(outputFilePath);
                    long fileSize = outputFile.length();
                    String fileName = outputFile.getName();

                    log.info("语音合成成功，文件: {}, 大小: {} bytes", outputFilePath, fileSize);
                    return new SpeechSynthesisResult(true, "语音合成成功", outputFilePath, fileName, fileSize);

                } else if (result.getReason() == ResultReason.Canceled) {
                    SpeechSynthesisCancellationDetails cancellation = SpeechSynthesisCancellationDetails
                            .fromResult(result);

                    String errorMessage = "语音合成被取消: " + cancellation.getReason();
                    if (cancellation.getReason() == CancellationReason.Error) {
                        errorMessage += ", 错误代码: " + cancellation.getErrorCode() +
                                ", 错误详情: " + cancellation.getErrorDetails();
                    }

                    log.warn("尝试 {}/{} 失败: {}", attempt, maxRetries, errorMessage);

                    // 如果是连接失败或网络错误，可以重试
                    if (isRetryableError(cancellation) && attempt < maxRetries) {
                        try {
                            Thread.sleep(retryDelay * attempt); // 指数退避
                            continue;
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            return new SpeechSynthesisResult(false, "重试被中断: " + ie.getMessage());
                        }
                    } else {
                        return new SpeechSynthesisResult(false, errorMessage);
                    }
                } else {
                    String errorMessage = "语音合成失败，原因: " + result.getReason();
                    log.warn("尝试 {}/{} 失败: {}", attempt, maxRetries, errorMessage);

                    if (attempt < maxRetries) {
                        try {
                            Thread.sleep(retryDelay * attempt);
                            continue;
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            return new SpeechSynthesisResult(false, "重试被中断: " + ie.getMessage());
                        }
                    } else {
                        return new SpeechSynthesisResult(false, errorMessage);
                    }
                }

            } catch (InterruptedException | ExecutionException | java.util.concurrent.TimeoutException e) {
                String errorMessage = "语音合成异常: " + e.getMessage();
                log.warn("尝试 {}/{} 异常: {}", attempt, maxRetries, errorMessage, e);

                if (isRetryableException(e) && attempt < maxRetries) {
                    try {
                        Thread.sleep(retryDelay * attempt);
                        continue;
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return new SpeechSynthesisResult(false, "重试被中断: " + ie.getMessage());
                    }
                } else {
                    return new SpeechSynthesisResult(false, errorMessage);
                }
            }
        }

        return new SpeechSynthesisResult(false, "所有重试都失败了");
    }

    /**
     * 使用本地 Ollama 服务合成 TTS 并将结果写入文件
     */
    private SpeechSynthesisResult synthesizeWithOllama(String text, String outputFilePath) {
        try {
            initializeOutputDirectory();

            // 构造请求 - 假设 Ollama 支持类似 /api/response 或 /api/generate 的接口，这里使用 /api/generate
            String url = ollamaBaseUrl + "/api/generate";

            // 构造 JSON body（基于 Ollama 常见 API）
            String json = "{\"model\":\"" + ollamaModelName + "\"," +
                    "\"messages\":[{\"role\":\"user\",\"content\":\"TTS: " +
                    text.replaceAll("\\\"", "\\\\\"") + "\"}]," +
                    "\"modalities\":[\"audio\"],\"audio_format\":\"" + ollamaFormat + "\"}";

            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(json))
                    .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200 && response.statusCode() != 201) {
                return new SpeechSynthesisResult(false,
                        "Ollama 返回错误: " + response.statusCode() + " " + response.body());
            }

            // 响应可能包含 base64 编码的音频或直接二进制（取决于 Ollama API）
            String body = response.body();

            // 尝试从 body 中提取 base64 音频片段（常见字段名 audio 或 audio_base64）
            Pattern p = Pattern
                    .compile("(?i)\\\"?(?:audio|audio_base64)\\\"?\\s*[:=]\\s*\\\"([A-Za-z0-9+/=\\\\n\\r]+)\\\"");
            Matcher m = p.matcher(body);
            byte[] audioBytes = null;
            if (m.find()) {
                String b64 = m.group(1).replaceAll("\\\\n|\\\\r", "");
                audioBytes = Base64.getDecoder().decode(b64);
            } else {
                // 如果返回 JSON 包含字段 'data' 或其他嵌套结构，尝试简单处理或返回错误
                // 作为回退，若 body 看起来像 base64 则直接解码
                String trimmed = body.trim();
                if (trimmed.matches("^[A-Za-z0-9+/=\\n\\r]+$")) {
                    audioBytes = Base64.getDecoder().decode(trimmed.replaceAll("\\\\n|\\\\r", ""));
                } else {
                    return new SpeechSynthesisResult(false, "无法解析 Ollama 返回的音频数据，请检查 Ollama API 响应: " + body);
                }
            }

            // 写入文件
            Path outPath = Paths.get(outputFilePath);
            Files.write(outPath, audioBytes);

            File outFile = outPath.toFile();
            return new SpeechSynthesisResult(true, "合成成功", outFile.getAbsolutePath(), outFile.getName(),
                    outFile.length());

        } catch (Exception e) {
            log.error("调用 Ollama 合成失败: {}", e.getMessage(), e);
            return new SpeechSynthesisResult(false, "调用 Ollama 合成失败: " + e.getMessage());
        }
    }

    /**
     * 判断是否是可重试的错误
     */
    private boolean isRetryableError(SpeechSynthesisCancellationDetails cancellation) {
        if (cancellation.getReason() == CancellationReason.Error) {
            String errorDetails = cancellation.getErrorDetails();
            // 网络连接错误通常可以重试
            return errorDetails != null && (errorDetails.contains("ConnectionFailure") ||
                    errorDetails.contains("Connection failed") ||
                    errorDetails.contains("WS_OPEN_ERROR") ||
                    errorDetails.contains("timeout") ||
                    errorDetails.contains("network"));
        }
        return false;
    }

    /**
     * 判断是否是可重试的异常
     */
    private boolean isRetryableException(Exception e) {
        return e instanceof java.util.concurrent.TimeoutException ||
                (e.getCause() != null &&
                        (e.getCause().getMessage().contains("Connection") ||
                                e.getCause().getMessage().contains("timeout") ||
                                e.getCause().getMessage().contains("network")));
    }

    /**
     * 使用默认语音合成
     */
    public SpeechSynthesisResult synthesizeSpeech(String text, String outputFilePath) {
        return synthesizeSpeech(text, outputFilePath, defaultVoice);
    }

    /**
     * 为AI生成的文章创建语音文件（自定义语音）
     * 
     * @param articleText  AI生成的文章内容
     * @param articleTitle 文章标题（可选，用于文件命名）
     * @param voiceName    自定义语音名称
     * @return 语音合成结果
     */
    public SpeechSynthesisResult synthesizeArticleWithVoice(String articleText, String articleTitle, String voiceName) {
        if (articleText == null || articleText.trim().isEmpty()) {
            return new SpeechSynthesisResult(false, "文章内容不能为空");
        }

        try {
            initializeOutputDirectory();

            // 生成文件名
            String prefix = (articleTitle != null && !articleTitle.trim().isEmpty())
                    ? articleTitle.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_")
                    : "article";
            String fileName = generateFileName(prefix);
            String filePath = Paths.get(outputDirectory, fileName).toString();

            return synthesizeSpeech(articleText, filePath, voiceName);

        } catch (IOException e) {
            log.error("初始化输出目录失败: {}", e.getMessage());
            return new SpeechSynthesisResult(false, "初始化输出目录失败: " + e.getMessage());
        }
    }

    /**
     * 支持的语音列表
     */
    public static class SupportedVoice {
        public static final String ZH_CN_XIAOXIAO = "zh-CN-XiaoxiaoNeural";
        public static final String ZH_CN_YUNXI = "zh-CN-YunxiNeural";
        public static final String ZH_CN_YUNYANG = "zh-CN-YunyangNeural";
        public static final String EN_US_ARIA = "en-US-AriaNeural";
        public static final String EN_US_JENNY = "en-US-JennyNeural";
        public static final String EN_US_GUY = "en-US-GuyNeural";
    }
}
