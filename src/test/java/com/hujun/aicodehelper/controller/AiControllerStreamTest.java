package com.hujun.aicodehelper.controller;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.Duration;

/**
 * 流式响应测试
 * 用于验证RAG流式聊天的修复效果
 */
@SpringBootTest
@ActiveProfiles("test")
public class AiControllerStreamTest {

    /**
     * 测试流式响应是否正确结束
     * 这个测试模拟了流式响应的完整生命周期
     */
    @Test
    public void testStreamResponseWithEndMarker() {
        // 模拟一个包含结束标识的流式响应
        Flux<ServerSentEvent<String>> mockStream = Flux.just(
                ServerSentEvent.<String>builder().data("第一段响应").build(),
                ServerSentEvent.<String>builder().data("第二段响应").build(),
                ServerSentEvent.<String>builder().data("第三段响应").build(),
                ServerSentEvent.<String>builder().event("end").data("[DONE]").build()
        );

        // 验证流式响应的完整性
        StepVerifier.create(mockStream)
                .expectNextMatches(event -> "第一段响应".equals(event.data()))
                .expectNextMatches(event -> "第二段响应".equals(event.data()))
                .expectNextMatches(event -> "第三段响应".equals(event.data()))
                .expectNextMatches(event -> 
                    "end".equals(event.event()) && "[DONE]".equals(event.data()))
                .verifyComplete();
    }

    /**
     * 测试错误响应的处理
     */
    @Test
    public void testStreamResponseWithError() {
        // 模拟包含错误的流式响应
        Flux<ServerSentEvent<String>> mockErrorStream = Flux.just(
                ServerSentEvent.<String>builder().data("开始响应").build(),
                ServerSentEvent.<String>builder().data("{\"error\": \"处理请求时发生错误，请稍后重试\"}").build()
        );

        // 验证错误处理
        StepVerifier.create(mockErrorStream)
                .expectNextMatches(event -> "开始响应".equals(event.data()))
                .expectNextMatches(event -> 
                    event.data() != null && event.data().contains("error"))
                .verifyComplete();
    }

    /**
     * 测试超时场景
     */
    @Test
    public void testStreamResponseTimeout() {
        // 模拟一个延迟很长的流式响应
        Flux<ServerSentEvent<String>> slowStream = Flux.just(
                ServerSentEvent.<String>builder().data("开始响应").build()
        ).delayElements(Duration.ofSeconds(2));

        // 验证在合理时间内能收到响应
        StepVerifier.create(slowStream)
                .expectNextMatches(event -> "开始响应".equals(event.data()))
                .verifyComplete();
    }

    /**
     * 测试空内容过滤
     */
    @Test
    public void testEmptyContentFiltering() {
        // 模拟包含空内容的流式响应
        Flux<String> rawStream = Flux.just("有效内容", "", "   ", "另一个有效内容", null, "最后的内容");
        
        // 应用过滤逻辑（模拟控制器中的过滤）
        Flux<ServerSentEvent<String>> filteredStream = rawStream
                .filter(chunk -> chunk != null && !chunk.trim().isEmpty())
                .map(chunk -> ServerSentEvent.<String>builder().data(chunk).build());

        // 验证空内容被正确过滤
        StepVerifier.create(filteredStream)
                .expectNextMatches(event -> "有效内容".equals(event.data()))
                .expectNextMatches(event -> "另一个有效内容".equals(event.data()))
                .expectNextMatches(event -> "最后的内容".equals(event.data()))
                .verifyComplete();
    }
}
