{"permissions": {"allow": ["WebFetch(domain:docs.langchain4j.dev)", "Bash(mv src/main/java/com/hujun/aicodehelper/ai/model/QwenChatModelConfig.java src/main/java/com/hujun/aicodehelper/ai/model/GeminiChatModelConfig.java)", "Bash(grep -n \"wrapWordsWithHover\" /Users/<USER>/Desktop/RAG/ai-code-helper-master/ai-code-helper-frontend/src/components/StudyMode.vue)", "Bash(grep -n \"readNextItem\" /Users/<USER>/Desktop/RAG/ai-code-helper-master/ai-code-helper-frontend/src/components/StudyMode.vue)", "Bash(grep -n \"isAutoReading\" /Users/<USER>/Desktop/RAG/ai-code-helper-master/ai-code-helper-frontend/src/components/StudyMode.vue)", "mcp__filesystem__list_directory", "mcp__filesystem__read_text_file", "mcp__filesystem__search_files", "mcp__filesystem__write_file", "mcp__filesystem__edit_file", "mcp__filesystem__move_file", "mcp__gemini-cli__ask-gemini", "mcp__codebase__search_codebase"], "deny": []}}